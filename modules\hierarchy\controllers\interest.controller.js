const {
  JWT_SECRET,
  SYSTEM_ROLES,
  LADIES_PHONE_VALDIATION,
} = require("../../../constants");
const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  UPDATE_SUCCESS,
  NOT_FOUND,
  CUSTOM_ERROR,
  UPDATE_ERROR,
} = require("../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const {
  redisCacheKeys,
  setCache,
  getCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const { KGUser, SystemRole, ArazCity } = require("../../hierarchy/models");
const { Interest } = require("../models/interest.model");
const jwt = require("jsonwebtoken");
const { assignKGUserV2 } = require("./kgUser.controller");
const { kgPoolAggregation } = require("../aggregations/kgPool.aggregation");
const constants = require("../../../constants");
const { extractPermission } = require("../../../middlewares/guard.middleware");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const addEditInterest = apiHandler(async (req, res) => {
  const { id } = req.body;
  const data = { ...req.body, status: "not-assigned" };

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    data.miqaatID,
    data.arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let interestData;

  if (id) {
    interestData = await Interest.findByIdAndUpdate(id, data, {
      new: true,
    });

    if (!interestData) return apiResponse(NOT_FOUND, "Interest", null, res);
  } else {
    const existingInterest = await Interest.findOne({
      ITSID: data.ITSID,
      miqaatID: toObjectId(data.miqaatID),
      arazCityID: toObjectId(data.arazCityID),
    });

    if (existingInterest)
      return apiResponse(
        CUSTOM_ERROR,
        "Interest already exist for the User",
        null,
        res
      );

    interestData = await Interest.create(data);
  }

  let userData = await KGUser.findOne({ ITSID: data.ITSID })
    .populate("jamiatID", "name")
    .populate("jamaatID", "name")
    .lean();

  userData.interestData = interestData;
  userData.id = userData._id;
  userData.notAssignedArazCityID = interestData.arazCityID;
  userData.notAssignedMiqaatID = interestData.miqaatID;

  const token = jwt.sign(userData, JWT_SECRET, { expiresIn: "7d" });

  return apiResponse(id ? UPDATE_SUCCESS : ADD_SUCCESS, "Interest", token, res);
});

const getSingleInterest = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const interestData = await Interest.findOne({
    userID: req.user._id,
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
  })
    .populate("interestOne.departmentID", "name _id")
    .populate("interestTwo.departmentID", "name _id")
    .populate("interestThree.departmentID", "name _id")
    .populate("interestOne.functionID", "name _id")
    .populate("interestTwo.functionID", "name _id")
    .populate("interestThree.functionID", "name _id")
    .populate("arazCityZoneID", "name _id")
    .populate({
      path: "userID",
      populate: [
        { path: "jamiatID", select: "name" },
        { path: "jamaatID", select: "name" },
      ],
    });

  return apiResponse(UPDATE_SUCCESS, "Interest", interestData, res);
});

const checkInterestStatus = apiHandler(async (req, res) => {
  const { ITSID, miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const interestData = await Interest.findOne({
    ITSID,
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
  });
  if (!interestData || interestData.status !== "assigned") {
    return apiError(CUSTOM_ERROR, "Khidmat is not assigned yet", null, res);
  }

  let userData = await KGUser.findOne({ ITSID: ITSID })
    .populate("jamiatID", "name")
    .populate("jamaatID", "name")
    .lean();

  userData.interestData = interestData;

  userData.id = userData._id;
  userData.notAssignedArazCityID = interestData.arazCityID;
  userData.notAssignedMiqaatID = interestData.miqaatID;
  const token = jwt.sign(userData, JWT_SECRET, { expiresIn: "7d" });
  return apiResponse(UPDATE_SUCCESS, "Interest", token, res);
});


const getAllInterests = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.params;
  const loggedInUser = req.user;
  const cacheKey = `${redisCacheKeys.HIERARCHY}:${redisCacheKeys.INTEREST}:${arazCityID}:${miqaatID}`;

  let data = await getCache(cacheKey,miqaatID,arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Interests", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const kgUsers = await KGUser.find({
    "miqaats.arazCityID": arazCityID,
    "miqaats.miqaatID": miqaatID,
  })
    .populate("jamiatID", "name")
    .populate("jamaatID", "name")
    .populate({
      path: "miqaats.hierarchyPositionID",
      select: "name alias",
    })
    .populate({
      path: "miqaats.departmentID",
      select: "name",
    })
    .populate({
      path: "miqaats.functionID",
      select: "name",
    })
    .populate({
      path: "miqaats.arazCityID",
      select: "name showPositionAlias",
    })
    .populate({
      path: "miqaats.arazCityZoneID",
      select: "name",
    })
    .populate({
      path: "miqaats.kgTypeID",
      select: "name",
    })
    .populate({
      path: "miqaats.kgGroupID",
      select: "name",
    })
    .lean();

  const kgUserIDs = kgUsers.map((user) => user._id);
  const interests = await Interest.find({ userID: { $in: kgUserIDs } })
    .populate({
      path: "interestOne.departmentID",
      select: "name",
    })
    .populate({
      path: "interestTwo.departmentID",
      select: "name",
    })
    .populate({
      path: "interestThree.departmentID",
      select: "name",
    })
    .populate({
      path: "interestThree.departmentID",
      select: "name",
    })
    .populate({
      path: "arazCityZoneID",
      select: "name",
    })
    .lean();

  let allowPhone = false;

  const LADIES_RULE = LADIES_PHONE_VALDIATION.find(
    (rule) => rule.arazCityID.toString() === arazCityID.toString()
  );

  if (LADIES_RULE) {
    if (loggedInUser.systemRoleID) {
      allowPhone = LADIES_RULE.permission.includes(
        loggedInUser.systemRoleID.toString()
      );
    } else {
      const matchedMiqaat = loggedInUser.miqaats.find(
        (m) =>
          m.arazCityID?.toString() === arazCityID.toString() &&
          LADIES_RULE.permission.includes(m.cityRoleID?.toString())
      );
      if (matchedMiqaat) {
        allowPhone = true;
      }
    }
  }

  kgUsers.forEach((user) => {
    const interest = interests.find(
      (i) => i.userID.toString() === user._id.toString()
    );
    const miqaat = user.miqaats.find(
      (miqaat) =>
        miqaat.miqaatID?._id.toString() === miqaatID.toString() &&
        miqaat.arazCityID?._id.toString() === arazCityID.toString()
    );
    if (
      miqaat.isActive &&
      !isEmpty(miqaat.hierarchyPositionID) &&
      miqaat.status != "DELETED"
    ) {
      user.status = "assigned";
    } else {
      user.status = "not-assigned";
    }
    user.miqaats = miqaat;
    user.interestData = interest || null;
    if (user.gender === "F") {
      user.phone = allowPhone ? user.phone : null;
      user.whatsapp = allowPhone ? user.whatsapp : null;
    } else {
      user.phone = user.phone;
      user.whatsapp = user.whatsapp;
    }
  });

  apiResponse(FETCH, "Interests", kgUsers, res);
  await setCache(cacheKey, kgUsers, miqaatID, arazCityID);
});

const getAllInterestsByPagination = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.params;
  if (!arazCityID || !miqaatID) {
    return apiError(
      CUSTOM_ERROR,
      "Please provide arazCityID and miqaatID",
      null,
      res
    );
  }
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const { page, limit } = req.body;

  const isPaginationProvided =
    page !== undefined &&
    page !== null &&
    limit !== undefined &&
    limit !== null;

  const pageNum = isPaginationProvided ? parseInt(page) : 1;
  const limitNum = isPaginationProvided ? parseInt(limit) : 0; // 0 means no limit
  const skip = isPaginationProvided ? (pageNum - 1) * limitNum : 0;

  const cacheKey = `${redisCacheKeys.HIERARCHY}:${
    redisCacheKeys.INTEREST
  }:${arazCityID}:${miqaatID}:${JSON.stringify(req.body)}`;

  let data = await getCache(cacheKey, miqaatID, arazCityID, true);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Interests", data, res);
  }

  const pipeline = kgPoolAggregation(req);

  const result = await KGUser.aggregate(pipeline);

  const users = isPaginationProvided ? result[0].data : result || [];
  const totalCount = isPaginationProvided
    ? result[0].count.length > 0
      ? result[0].count[0].total
      : 0
    : 0;

  const updatedKGUsers = await applyPhoneVisibilityRules(users, req);

  const responseData = {
    users: updatedKGUsers,
    ...(isPaginationProvided && {
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(totalCount / limitNum),
        totalCount: totalCount,
        limit: limitNum,
        hasNextPage: pageNum < Math.ceil(totalCount / limitNum),
        hasPrevPage: pageNum > 1,
        showing: `${skip + 1}-${Math.min(
          skip + limitNum,
          totalCount
        )} of ${totalCount}`,
      },
    }),
  };

  apiResponse(FETCH, "Interests", responseData, res);
  await setCache(cacheKey, responseData, miqaatID, arazCityID); // Cache for 5 minutes
});

const applyPhoneVisibilityRules = async (users, req) => {
  if (users.length > 0 && users[0].data && users[0].data.length > 0) {
    const data = users[0].data.map((user) => {
      if (user.gender === "M") {
        return user;
      }

      if (!req.allowPhone) {
        user.phone = undefined;
        user.whatsapp = undefined;
      }

      if (!req.allowFemalePhoto) {
        user.logo = undefined;
      }

      return user;
    });
    return [{ data }];
  }
  return users.map((user) => {
    if (user.gender === "M") {
      return user;
    }

    if (!req.allowPhone) {
      return {
        ...user,
        phone: undefined,
        whatsapp: undefined,
      };
    }

    if (!req.allowFemalePhoto) {
      return {
        ...user,
        logo: undefined,
      };
    }

    return user;
  });
};

async function getSystemUserMiqaats() {
  const cities = await ArazCity.find(
    { status: true },
    "_id name miqaatID jamiats jamaats"
  )
    .populate("miqaatID", "_id name isActive")
    .sort({ _id: -1 });

  const map = new Map();

  for (const { miqaatID, _id, name, jamiats, jamaats } of cities) {
    if (!map.has(miqaatID._id)) {
      map.set(miqaatID._id, {
        _id: miqaatID._id,
        name: miqaatID.name,
        arazCities: [],
      });
    }

    map.get(miqaatID._id).arazCities.push({ _id, name, jamiats, jamaats });
  }

  return Array.from(map.values());
}

function findMatchingMiqaatCity(systemUserMiqaats, jamiatID, jammatID) {
  for (const miqaat of systemUserMiqaats) {
    for (const city of miqaat.arazCities) {
      if (
        city.jamiats?.includes(jamiatID.toString()) &&
        city.jamaats?.includes(jammatID.toString())
      ) {
        return {
          miqaatID: miqaat._id,
          arazCityID: city._id,
        };
      }
    }
  }
  return null;
}

const assignKGUser = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, existsInDB } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let results = { success: [], failed: [] };

  for (const user of existsInDB) {
    try {
      const userID = user.id;
      if (isEmpty(req.user.systemRoleID)) {
        const systemUserMiqaats = await getSystemUserMiqaats();
        const match = findMatchingMiqaatCity(
          systemUserMiqaats,
          user?.jamiatID,
          user?.jamaatID
        );
        if (
          !match ||
          match.miqaatID.toString() !== miqaatID ||
          match.arazCityID.toString() !== arazCityID
        ) {
          results.failed.push({
            ITSID: user.ITSID,
            reason: "User does not belong to Miqaat and Araz City",
          });
          continue;
        }
      }

      function formatInterestField(departmentID) {
        return departmentID
          ? {
              departmentID: toObjectId(departmentID),
              functionID: null,
            }
          : null;
      }
      if (!user.interestOne && !user.interestTwo && !user.interestThree) {
        results.failed.push({
          ITSID: user.ITSID,
          reason: "Atleast one interest is required",
        });
        continue;
      }
      await Interest.findOneAndUpdate(
        {
          ITSID: user.ITSID,
          miqaatID: toObjectId(miqaatID),
          arazCityID: toObjectId(arazCityID),
        },
        {
          $set: {
            userID,
            ITSID: user.ITSID,
            miqaatID,
            arazCityID,
            arazCityZoneID: user.arazCityZoneID ? user.arazCityZoneID : null,
            interestOne: formatInterestField(user.interestOne),
            interestTwo: formatInterestField(user.interestTwo),
            interestThree: formatInterestField(user.interestThree),
            status: user.hierarchyPositionID ? "assigned" : "not-assigned",
          },
        },
        { upsert: true, new: true }
      );
      await KGUser.updateOne(
        {
          _id: userID,
          miqaats: {
            $not: {
              $elemMatch: {
                arazCityID: toObjectId(arazCityID),
                miqaatID: toObjectId(miqaatID),
              },
            },
          },
        },
        {
          $push: {
            miqaats: {
              arazCityID: toObjectId(arazCityID),
              miqaatID: toObjectId(miqaatID),
              cityRoleID: toObjectId(
                constants.SYSTEM_ROLES.NOT_ASSIGNED[0].toString()
              ),
            },
          },
        }
      );
      results.success.push({
        ITSID: user.ITSID,
        status: "Updated",
      });
    } catch (error) {
      console.log(error);
      results.failed.push({
        ITSID: user.ITSID,
        reason: "Something went wrong",
      });
    }
  }
  // if (results.success.length <= 0) {
  //   return apiError(UPDATE_ERROR, "Interest", null, res);
  // }

  apiResponse(UPDATE_SUCCESS, "Interest", results, res);
  await clearCacheByPattern(
    `${redisCacheKeys.HIERARCHY}:${redisCacheKeys.INTEREST}:*`,miqaatID, arazCityID
  );
});

module.exports = {
  addEditInterest,
  checkInterestStatus,
  getSingleInterest,
  getAllInterests,
  getAllInterestsByPagination,
  assignKGUser,
};
