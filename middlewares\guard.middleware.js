const jwt = require("jsonwebtoken");
const {
  isEmpty,
  decryptPermissions,
  objectifyModules,
  matchPatterns,
} = require("../utils/misc.util");
const { apiError, apiHandler } = require("../utils/api.util");
const {
  UNAUTHORIZED,
  NOT_FOUND,
  FORBIDDEN,
  CUSTOM_ERROR,
} = require("../utils/message.util");
const { JWT_SECRET, CRYPTO_SECRET, SYSTEM_ROLES } = require("../constants");
const { KGUser, SystemRole } = require("../modules/hierarchy/models");
const { ApiKey } = require("../public/models/apiKey.model");
const { getCache, setCache, redisCacheKeys } = require("../utils/redis.cache");

const authGuard = apiHandler(async (req, res, next) => {
  let publicRoutes = [
    "/global-master/notification/add",
    "/auth/user/its-one-login",
    "/hierarchy/healthcheck",
  ];
  if (publicRoutes.includes(req.url)) {
    next();
    return;
  }

  const token = req.header("Authorization");
  if (isEmpty(token)) {
    return apiError(UNAUTHORIZED, "User", null, res);
  }

  let decodedToken;

  try {
    decodedToken = jwt.verify(token, JWT_SECRET);
  } catch (error) {
    if (error.name === "TokenExpiredError") {
      return apiError(UNAUTHORIZED, "Token expired", null, res);
    } else if (error.name === "JsonWebTokenError") {
      return apiError(UNAUTHORIZED, "Invalid token", null, res);
    } else {
      return apiError(UNAUTHORIZED, "Authentication error", null, res);
    }
  }

  let userData = await KGUser.findById(decodedToken.id).lean();
  if (isEmpty(userData)) {
    return apiError(UNAUTHORIZED, "User", null, res);
  }

  req.user = { ...userData, isSystemUser: decodedToken.isSystemUser };
  next();
});

const extractPermission = (
  rawModules,
  module,
  page,
  action,
  subAction = null
) => {
  try {
    let modules;
    if (Array.isArray(rawModules)) {
      modules = objectifyModules(rawModules);
    } else if (
      rawModules &&
      typeof rawModules === "object" &&
      rawModules.modules
    ) {
      modules = objectifyModules(rawModules.modules);
    }

    // Handle regular permissions
    if (!subAction || subAction === "file") {
      if (
        !modules[module] ||
        !modules[module][page] ||
        !modules[module][page][action]
      )
        return false;
      return modules[module][page][action];
    }

    // Handle nested permissions like upload/hvac or approve/structure
    const permission = modules[module][page][action];

    // If it's an object with permission and subPermission properties
    if (
      permission &&
      typeof permission === "object" &&
      permission.permission !== undefined
    ) {
      // First check if the main permission is granted
      if (!permission.permission) return false;

      // If no subPermissions, just return the main permission value
      if (!permission.subPermission) return permission.permission;

      // Find the matching subPermission
      const subPermissionItem = permission.subPermission.find(
        (item) => item.uniqueName === subAction
      );

      // Return the subPermission permission value if found, otherwise false
      return subPermissionItem ? subPermissionItem.permission : false;
    }

    return false;
  } catch (error) {
    console.log(error);
    return null;
  }
};

const skipRoleGuardUrls = [
  "/global-master/miqaat/get/is-active",
  "/global-master/notification/get/:id",
  "/global-master/notification/get",
  "/global-master/notification/add",
  "/auth/user/its-one-login",
  "/hierarchy/kg-user/add/one-signal-device",
  "/hierarchy/healthcheck",
  "/auth/user/get/encrypted-permissions",
  "/auth/user/get/encrypted-permissions2",
  "/hierarchy/jamiat-jamaat",
  "/auth/user/get/profile",
  "/hierarchy/kg-user/edit/update-consent-status",
  "/ashara-guide/guide/add/click",
  "/hr/interest/add",
  "/hr/interest/get",
  "/hr/interest/get/interest-status",
  "/hierarchy/interest/add",
  "/hierarchy/interest/get",
  "/hierarchy/interest/get/interest-status",
  "/hierarchy/department/get/by-araz-city",
  "/hierarchy/function/get/by-department",
  "/hierarchy/system-role/get",
  "/activity/analytics-log/add",
  "/attendance",
];

const superAdminOnlyUrls = [
  "/hierarchy/system-user",
  "/hierarchy/system-role",
  "/hierarchy/kg-hierarchy-position/edit/save-weightage",
  "/ashara-guide/guide/add",
  "/ashara-guide/guide/edit",
  "/ashara-guide/guide/delete",
  "/ashara-guide/guide/get/auth/user-click-report",
];

const roleGuard = apiHandler(async (req, res, next) => {
  let isSuperAdmin = false;
  isSuperAdmin =
    req?.user?.systemRoleID?.toString() ===
    SYSTEM_ROLES.SUPER_ADMIN[0].toString();

  if (isSuperAdmin) {
    let allowPhone = false;
    let allowFemalePhoto = false;
    const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:${req.user.systemRoleID}`;
    let roleData = await getCache(cachekey);
    if (isEmpty(roleData)) {
      roleData = await SystemRole.findById(req.user.systemRoleID);
      await setCache(cachekey, roleData);
    }
    allowPhone = await extractPermission(
      roleData,
      "hierarchy",
      "show-female-phone",
      "view"
    );
    allowFemalePhoto = await extractPermission(
      roleData,
      "hierarchy",
      "show-female-photo",
      "view"
    );
    req.allowPhone = allowPhone;
    req.allowFemalePhoto = allowFemalePhoto;
  }
  if (
    isSuperAdmin ||
    (matchPatterns(req.url, skipRoleGuardUrls) &&
      !req.url.startsWith("/hierarchy/interest/get-all-v2"))
  ) {
    return next();
  }

  if (matchPatterns(req.url, superAdminOnlyUrls) && !isSuperAdmin) {
    return apiError(FORBIDDEN, "User", null, res);
  }

  const encryptedPermissions = req?.header("Permissions")?.trim();

  if (isEmpty(encryptedPermissions)) {
    return apiError(NOT_FOUND, "Permissions", null, res);
  }

  const decryptedData = decryptPermissions(encryptedPermissions, CRYPTO_SECRET);
  if (isEmpty(decryptedData)) {
    return apiError(UNAUTHORIZED, "User", null, res);
  }

  let systemRole;
  try {
    systemRole = JSON.parse(decryptedData);
  } catch (error) {
    return apiError(UNAUTHORIZED, "User", null, res);
  }
  if (isEmpty(systemRole.modules)) {
    const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.SYSTEM_ROLES}:${systemRole.systemRoleID}`;
    let roleData = await getCache(cachekey);
    if (isEmpty(roleData)) {
      roleData = await SystemRole.findById(systemRole.systemRoleID);
      await setCache(cachekey, roleData);
    }

    systemRole.modules = roleData;
  }

  let url = req.url.split("/");

  let module = url[1];
  let page = url[2];
  let action = url[3];
  let subAction = url[4];

  action = action === "get" ? "view" : action;
  let allowAccess;
  if ((action === "upload" || action === "approve") && subAction) {
    allowAccess = extractPermission(
      systemRole.modules,
      module,
      page,
      action,
      subAction
    );
  } else {
    allowAccess = extractPermission(systemRole.modules, module, page, action);
  }

  let allowPhone = false;
  let allowFemalePhoto = false;

  allowPhone = await extractPermission(
    systemRole.modules,
    "hierarchy",
    "show-female-phone",
    "view"
  );

  allowFemalePhoto = await extractPermission(
    systemRole.modules,
    "hierarchy",
    "show-female-photo",
    "view"
  );
  req.allowPhone = allowPhone;
  req.allowFemalePhoto = allowFemalePhoto;

  if (req.url.startsWith("/hierarchy/interest/get-all-v2")) {
    allowAccess = true;
  }
  if (isEmpty(allowAccess) || !allowAccess) {
    return apiError(FORBIDDEN, "User", null, res);
  } else {
    next();
  }
});
const validateApiKey = apiHandler(async (req, res, next) => {
  const apiKey = req.header("x-api-key");
  if (isEmpty(apiKey)) {
    return apiError(CUSTOM_ERROR, "Api Key Required", null, res);
  }
  const apiKeys = await ApiKey.findOne({ apiKey });
  // const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.PUBLIC_API_KEY}:${apiKey}`;
  // let apiKeys = await getCache(cachekey);
  // if (isEmpty(apiKeys)) {
  //   await setCache(cachekey, apiKeys);
  // }
  if (isEmpty(apiKeys)) {
    return apiError(CUSTOM_ERROR, "Invalid API key", null, res);
  }

  apiKeys.requestCount += 1;
  await apiKeys.save();
  next();
});

module.exports = {
  authGuard,
  roleGuard,
  validateApiKey,
  extractPermission,
};
