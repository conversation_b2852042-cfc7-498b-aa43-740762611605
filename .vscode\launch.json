{"version": "0.2.0", "compounds": [{"name": "Start All Services", "configurations": ["Gateway", "Global", "Hierarchy", "Communication", "Task Management", "Survey", "Auth Service"]}], "configurations": [{"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Gateway", "program": "${workspaceFolder}/gateway.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Global", "program": "${workspaceFolder}/services/global/global.service.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Hierarchy", "program": "${workspaceFolder}/services/hierarchy.service.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Communication", "program": "${workspaceFolder}/services/communication.service.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Task Management", "program": "${workspaceFolder}/services/taskManagement.service.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Survey", "program": "${workspaceFolder}/services/survey/survey.service", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Auth Service", "program": "${workspaceFolder}/services/auth.service.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}]}