const {
  api<PERSON><PERSON><PERSON>,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  FETCH,
  NOT_FOUND,
  CUSTOM_ERROR,
  CUSTOM_SUCCESS,
} = require("../../../utils/message.util");
const { isEmpty } = require("../../../utils/misc.util");
const { KgRequisition, ArazCity, Interest } = require("../models");

const listRequisitions = apiHandler(async (req, res) => {
  // Updated query to match the actual schema structure
  let requisitionFilter = {};

  const { miqaatID, arazCityID } = req.query;

  if (miqaatID && arazCityID) {
    const checkActiveStatus = await isActiveMiqaatAndArazCity(
      miqaatID,
      arazCityID,
      req
    );
    if (!checkActiveStatus) {
      return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
    }

    requisitionFilter = {
      miqaatID,
      arazCityID,
    };
  } else {
    const arazCities = await ArazCity.find({
      status: true,
      jamiats: req.user.jamiatID, // Query array field
      $or: [
        { jamaats: req.user.jamaatID }, // Query array field
        { jamaats: "all" },
      ],
    });

    if (arazCities.length === 0) {
      return apiError(NOT_FOUND, "Araz Cities", null, res);
    }

    const arazCityIDs = arazCities.map((city) => city._id);
    requisitionFilter = { arazCityID: { $in: arazCityIDs } };
  }

  const requisitions = await KgRequisition.find(requisitionFilter)
    .populate(["arazCityZoneID", "departmentID"])
    .select(
      "arazCityZoneID departmentID khidmatGuzaarCount purpose _id appliedCount status applications miqaatID arazCityID"
    );

  if (requisitions.length === 0) {
    return apiError(NOT_FOUND, "No Requisitions Found", null, res);
  }

  const modifiedRequisitions = requisitions.map((requisition) => {
    const application = requisition.applications?.find(
      (application) =>
        application &&
        application.applicant.toString() === req.user._id.toString()
    );

    const reqObj = requisition.toObject();
    const data = {};
    data.department = reqObj.departmentID?.name;

    if (application) {
      data.status = "Already Applied";
    } else if (reqObj.status === "Closed") {
      data.status = "Closed";
    } else {
      data.status = reqObj.status;
    }

    data.id = reqObj._id;
    data.purpose = reqObj.purpose;
    data.khidmatGuzaarCount = reqObj.khidmatGuzaarCount;
    data.appliedCount = reqObj.applications?.length || 0;
    data.arazCityZone = reqObj.arazCityZoneID?.name;
    data.miqaatID = reqObj.miqaatID;
    data.arazCityID = reqObj.arazCityID;

    return data;
  });

  return apiResponse(FETCH, "Requisitions", modifiedRequisitions, res);
});

const applyForRequisition = apiHandler(async (req, res) => {
  const { requisitionID } = req.body;
  const userID = req.user._id;

  const requisition = await KgRequisition.findById(requisitionID);

  if (!requisition) {
    return apiError(NOT_FOUND, "Requisition Not Found", null, res);
  }

  if (requisition.status !== "Open") {
    return apiError(
      CUSTOM_ERROR,
      "This requisition is no longer accepting applications",
      null,
      res
    );
  }

  const alreadyApplied =
    requisition.applications?.some(
      (application) =>
        application && application.applicant?.toString() === userID.toString()
    ) ?? false;

  if (alreadyApplied) {
    return apiError(CUSTOM_ERROR, "Already Applied", null, res);
  }

  const interest = await Interest.findOne({ userID });

  if (isEmpty(interest)) {
    const newInterest = await Interest.create({
      userID,
      miqaatID: requisition.miqaatID,
      arazCityID: requisition.arazCityID,
      ITSID: req.user.ITSID,
      status: "not-assigned",
      interestOne: {
        departmentID: requisition.departmentID,
      },
    });
    newInterest.save();
  }

  const updatedRequisition = await KgRequisition.findByIdAndUpdate(
    requisitionID,
    {
      $push: {
        applications: {
          applicant: userID,
          status: "Applied",
          appliedAt: new Date(),
        },
      },
    },
    { new: true }
  );

  return apiResponse(
    CUSTOM_SUCCESS,
    "Application submitted successfully",
    updatedRequisition,
    res
  );
});

module.exports = {
  listRequisitions,
  applyForRequisition,
};
