const { validate } = require("../../../middlewares/validation.middleware");
const {
  addEditInterest,
  getSingleInterest,
  getAllInterests,
  checkInterestStatus,
  getAllInterestsByPagination,
  assignKGUser,
  getSingleInterestV2,
} = require("../controllers/interest.controller");
const { importITSUsers } = require("../controllers/kgUser.controller");
const {
  addEditInterestSchema,
  getSingleInterestSchema,
  getAllInterestsSchema,
  checkInterestStatusSchema,
  getAllInterestsByPaginationSchema,
  getSingleInterestSchemaV2,
  checkInterestStatusSchemaV2,
} = require("../validations/interest.validation");
const {
  importITSUsersSchema,
  assignKGUserSchema,
} = require("../validations/kgUser.validation");

const router = require("express").Router();

router.post("/add", validate(addEditInterestSchema, "body"), addEditInterest);
router.get(
  "/get/:ITSID",
  validate(getSingleInterestSchema, "params"),
  getSingleInterest
);

router.post(
  "/get/interest-status",
  validate(checkInterestStatusSchemaV2, "body"),
  checkInterestStatus
);
router.post("/add", validate(addEditInterestSchema, "body"), addEditInterest);
router.post(
  "/get",
  validate(getSingleInterestSchema, "body"),
  getSingleInterest
);

router.get(
  "/get-all/:arazCityID/:miqaatID",
  validate(getAllInterestsSchema, "params"),
  getAllInterests
);
router.post(
  "/get-all-v2/:arazCityID/:miqaatID",
  validate(getAllInterestsByPaginationSchema, "body"),
  getAllInterestsByPagination
);

router.post(
  "/add/import-its-users",
  validate(importITSUsersSchema, "body"),
  importITSUsers
);
router.post("/add/assign", validate(assignKGUserSchema, "body"), assignKGUser);

module.exports = router;
